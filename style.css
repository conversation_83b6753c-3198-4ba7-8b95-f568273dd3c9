/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Animated Background */
.background-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -2;
}

.background-animation::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 25% 25%,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    ),
    radial-gradient(
      circle at 50% 10%,
      rgba(255, 255, 255, 0.05) 0.5px,
      transparent 0.5px
    );
  background-size: 100px 100px, 100px 100px, 100px 100px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(1deg);
  }
}

/* Container */
.container {
  max-width: 600px;
  margin: 40px auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
  animation: slideIn 0.8s ease-out;
  position: relative;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 20px;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    filter: drop-shadow(0 0 5px rgba(102, 126, 234, 0.3));
  }
  to {
    filter: drop-shadow(0 0 15px rgba(102, 126, 234, 0.6));
  }
}

/* Stats */
.stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px 20px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)
  );
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-number {
  display: block;
  font-size: 1.8rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

/* Input Section */
.input-section {
  margin-bottom: 30px;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.input-icon {
  color: #667eea;
  font-size: 1.2rem;
  margin-left: 8px;
}

#todo-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-family: "Poppins", sans-serif;
  background: transparent;
  outline: none;
  color: #333;
}

#todo-input::placeholder {
  color: #999;
}

#priority-select,
#due-date {
  padding: 8px 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.8);
  color: #333;
  outline: none;
  transition: all 0.3s ease;
}

#priority-select:focus,
#due-date:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#add-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

#add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

#add-btn:active {
  transform: translateY(0);
}

/* Filter Section */
.filter-section {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 25px;
}

.filter-btn {
  padding: 8px 20px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  background: rgba(255, 255, 255, 0.8);
  color: #667eea;
  border-radius: 20px;
  cursor: pointer;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 14px;
}

.filter-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Progress Section */
.progress-section {
  margin-bottom: 30px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 10px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  font-size: 14px;
  color: #667eea;
  font-weight: 600;
}

/* Todo List */
#todo-list {
  list-style: none;
  padding: 0;
  margin-bottom: 20px;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: slideInTask 0.5s ease-out;
  position: relative;
  overflow: hidden;
}

@keyframes slideInTask {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.todo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.todo-item.completed {
  opacity: 0.7;
  background: rgba(200, 200, 200, 0.3);
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #999;
}

.todo-item.priority-high {
  border-left: 4px solid #ff4757;
}

.todo-item.priority-medium {
  border-left: 4px solid #ffa502;
}

.todo-item.priority-low {
  border-left: 4px solid #2ed573;
}

.todo-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid #667eea;
  border-radius: 50%;
  margin-right: 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.todo-checkbox:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.1);
}

.todo-checkbox.checked {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}

.todo-checkbox.checked::after {
  content: "✓";
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.todo-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.todo-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.todo-meta {
  display: flex;
  gap: 15px;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.todo-priority {
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
}

.todo-priority.high {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.todo-priority.medium {
  background: rgba(255, 165, 2, 0.1);
  color: #ffa502;
}

.todo-priority.low {
  background: rgba(46, 213, 115, 0.1);
  color: #2ed573;
}

.todo-due-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.todo-due-date.overdue {
  color: #ff4757;
  font-weight: 600;
}

.todo-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 14px;
}

.edit-btn {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.edit-btn:hover {
  background: rgba(52, 152, 219, 0.2);
  transform: scale(1.1);
}

.delete-btn {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.delete-btn:hover {
  background: rgba(231, 76, 60, 0.2);
  transform: scale(1.1);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
  display: none;
}

.empty-state.show {
  display: block;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 20px;
  color: #ddd;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: #666;
}

.empty-state p {
  font-size: 1rem;
  color: #999;
}

/* Confetti Canvas */
#confetti-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    margin: 20px;
    padding: 30px 20px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .stats {
    gap: 15px;
  }

  .stat-item {
    padding: 10px 15px;
  }

  .input-wrapper {
    flex-direction: column;
    gap: 10px;
  }

  #priority-select,
  #due-date {
    width: 100%;
  }

  .filter-section {
    flex-wrap: wrap;
    gap: 8px;
  }

  .todo-item {
    padding: 12px 16px;
  }

  .todo-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .stats {
    flex-direction: column;
    gap: 10px;
  }

  .stat-item {
    padding: 8px 12px;
  }

  .stat-number {
    font-size: 1.5rem;
  }
}
