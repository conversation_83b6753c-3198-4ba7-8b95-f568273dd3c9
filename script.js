document.addEventListener("DOMContentLoaded", function () {
  // DOM Elements
  const input = document.getElementById("todo-input");
  const addBtn = document.getElementById("add-btn");
  const todoList = document.getElementById("todo-list");
  const prioritySelect = document.getElementById("priority-select");
  const dueDateInput = document.getElementById("due-date");
  const filterBtns = document.querySelectorAll(".filter-btn");
  const emptyState = document.getElementById("empty-state");
  const progressFill = document.getElementById("progress-fill");
  const progressText = document.getElementById("progress-text");
  const totalTasksEl = document.getElementById("total-tasks");
  const completedTasksEl = document.getElementById("completed-tasks");
  const pendingTasksEl = document.getElementById("pending-tasks");
  const confettiCanvas = document.getElementById("confetti-canvas");

  // State
  let todos = JSON.parse(localStorage.getItem("awesome-todos")) || [];
  let currentFilter = "all";
  let editingId = null;

  // Confetti Animation
  const ctx = confettiCanvas.getContext("2d");
  let confettiParticles = [];

  function resizeCanvas() {
    confettiCanvas.width = window.innerWidth;
    confettiCanvas.height = window.innerHeight;
  }

  function createConfetti() {
    const colors = ["#667eea", "#764ba2", "#ff4757", "#ffa502", "#2ed573"];
    for (let i = 0; i < 50; i++) {
      confettiParticles.push({
        x: Math.random() * confettiCanvas.width,
        y: -10,
        vx: (Math.random() - 0.5) * 4,
        vy: Math.random() * 3 + 2,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: Math.random() * 6 + 2,
        rotation: Math.random() * 360,
        rotationSpeed: (Math.random() - 0.5) * 10,
      });
    }
  }

  function animateConfetti() {
    ctx.clearRect(0, 0, confettiCanvas.width, confettiCanvas.height);

    for (let i = confettiParticles.length - 1; i >= 0; i--) {
      const particle = confettiParticles[i];

      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.rotation += particle.rotationSpeed;
      particle.vy += 0.1; // gravity

      ctx.save();
      ctx.translate(particle.x, particle.y);
      ctx.rotate((particle.rotation * Math.PI) / 180);
      ctx.fillStyle = particle.color;
      ctx.fillRect(
        -particle.size / 2,
        -particle.size / 2,
        particle.size,
        particle.size
      );
      ctx.restore();

      if (particle.y > confettiCanvas.height) {
        confettiParticles.splice(i, 1);
      }
    }

    if (confettiParticles.length > 0) {
      requestAnimationFrame(animateConfetti);
    }
  }

  function triggerConfetti() {
    createConfetti();
    animateConfetti();
  }

  // Initialize
  resizeCanvas();
  window.addEventListener("resize", resizeCanvas);

  // Todo Functions
  function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  function saveTodos() {
    localStorage.setItem("awesome-todos", JSON.stringify(todos));
  }

  function formatDate(dateString) {
    if (!dateString) return "";
    const date = new Date(dateString);
    const today = new Date();
    const diffTime = date - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return "Overdue";
    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Tomorrow";
    return date.toLocaleDateString();
  }

  function isOverdue(dateString) {
    if (!dateString) return false;
    const date = new Date(dateString);
    const today = new Date();
    return date < today;
  }

  function addTodo(text, priority = "low", dueDate = "") {
    if (!text.trim()) return;

    const todo = {
      id: generateId(),
      text: text.trim(),
      completed: false,
      priority: priority,
      dueDate: dueDate,
      createdAt: new Date().toISOString(),
    };

    todos.unshift(todo);
    saveTodos();
    renderTodos();
    updateStats();

    // Clear inputs
    input.value = "";
    prioritySelect.value = "low";
    dueDateInput.value = "";
    input.focus();
  }

  function toggleTodo(id) {
    const todo = todos.find((t) => t.id === id);
    if (todo) {
      todo.completed = !todo.completed;
      if (todo.completed) {
        triggerConfetti();
      }
      saveTodos();
      renderTodos();
      updateStats();
    }
  }

  function deleteTodo(id) {
    todos = todos.filter((t) => t.id !== id);
    saveTodos();
    renderTodos();
    updateStats();
  }

  function editTodo(id) {
    const todo = todos.find((t) => t.id === id);
    if (todo) {
      input.value = todo.text;
      prioritySelect.value = todo.priority;
      dueDateInput.value = todo.dueDate;
      editingId = id;
      input.focus();
      addBtn.innerHTML = '<i class="fas fa-save"></i> Update';
    }
  }

  function updateTodo(id, text, priority, dueDate) {
    const todo = todos.find((t) => t.id === id);
    if (todo) {
      todo.text = text.trim();
      todo.priority = priority;
      todo.dueDate = dueDate;
      saveTodos();
      renderTodos();
      updateStats();

      // Reset editing state
      editingId = null;
      addBtn.innerHTML = '<i class="fas fa-plus"></i> Add Task';
    }
  }

  function renderTodos() {
    const filteredTodos = todos.filter((todo) => {
      if (currentFilter === "completed") return todo.completed;
      if (currentFilter === "pending") return !todo.completed;
      return true;
    });

    todoList.innerHTML = "";

    if (filteredTodos.length === 0) {
      emptyState.classList.add("show");
    } else {
      emptyState.classList.remove("show");
    }

    filteredTodos.forEach((todo) => {
      const li = document.createElement("li");
      li.className = `todo-item priority-${todo.priority}`;
      if (todo.completed) li.classList.add("completed");

      const dueDateFormatted = formatDate(todo.dueDate);
      const isTaskOverdue = isOverdue(todo.dueDate);

      li.innerHTML = `
        <div class="todo-checkbox ${
          todo.completed ? "checked" : ""
        }" onclick="toggleTodo('${todo.id}')"></div>
        <div class="todo-content">
          <div class="todo-text">${todo.text}</div>
          <div class="todo-meta">
            <span class="todo-priority ${todo.priority}">${todo.priority}</span>
            ${
              todo.dueDate
                ? `<span class="todo-due-date ${
                    isTaskOverdue ? "overdue" : ""
                  }">
              <i class="fas fa-calendar"></i> ${dueDateFormatted}
            </span>`
                : ""
            }
          </div>
        </div>
        <div class="todo-actions">
          <button class="action-btn edit-btn" onclick="editTodo('${
            todo.id
          }')" title="Edit">
            <i class="fas fa-edit"></i>
          </button>
          <button class="action-btn delete-btn" onclick="deleteTodo('${
            todo.id
          }')" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      `;

      todoList.appendChild(li);
    });
  }

  function updateStats() {
    const total = todos.length;
    const completed = todos.filter((t) => t.completed).length;
    const pending = total - completed;
    const progress = total > 0 ? Math.round((completed / total) * 100) : 0;

    totalTasksEl.textContent = total;
    completedTasksEl.textContent = completed;
    pendingTasksEl.textContent = pending;
    progressFill.style.width = `${progress}%`;
    progressText.textContent = `${progress}% Complete`;

    // Animate numbers
    animateNumber(totalTasksEl, total);
    animateNumber(completedTasksEl, completed);
    animateNumber(pendingTasksEl, pending);
  }

  function animateNumber(element, targetNumber) {
    const currentNumber = parseInt(element.textContent) || 0;
    const increment = targetNumber > currentNumber ? 1 : -1;
    const duration = 300;
    const steps = Math.abs(targetNumber - currentNumber);
    const stepDuration = steps > 0 ? duration / steps : 0;

    if (steps === 0) return;

    let current = currentNumber;
    const timer = setInterval(() => {
      current += increment;
      element.textContent = current;

      if (current === targetNumber) {
        clearInterval(timer);
      }
    }, stepDuration);
  }

  function setFilter(filter) {
    currentFilter = filter;
    filterBtns.forEach((btn) => {
      btn.classList.remove("active");
      if (btn.dataset.filter === filter) {
        btn.classList.add("active");
      }
    });
    renderTodos();
  }

  // Event Listeners
  addBtn.addEventListener("click", function () {
    const text = input.value.trim();
    const priority = prioritySelect.value;
    const dueDate = dueDateInput.value;

    if (!text) {
      input.focus();
      return;
    }

    if (editingId) {
      updateTodo(editingId, text, priority, dueDate);
    } else {
      addTodo(text, priority, dueDate);
    }
  });

  input.addEventListener("keydown", function (e) {
    if (e.key === "Enter") {
      addBtn.click();
    }
    if (e.key === "Escape" && editingId) {
      // Cancel editing
      editingId = null;
      input.value = "";
      prioritySelect.value = "low";
      dueDateInput.value = "";
      addBtn.innerHTML = '<i class="fas fa-plus"></i> Add Task';
    }
  });

  filterBtns.forEach((btn) => {
    btn.addEventListener("click", () => {
      setFilter(btn.dataset.filter);
    });
  });

  // Global functions for onclick handlers
  window.toggleTodo = toggleTodo;
  window.deleteTodo = deleteTodo;
  window.editTodo = editTodo;

  // Initialize app
  renderTodos();
  updateStats();
});
