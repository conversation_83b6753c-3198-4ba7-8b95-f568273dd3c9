document.addEventListener("DOMContentLoaded", function () {
  const input = document.getElementById("todo-input");
  const addBtn = document.getElementById("add-btn");
  const todoList = document.getElementById("todo-list");

  function addTodo(text) {
    if (!text.trim()) return;
    const li = document.createElement("li");
    li.textContent = text;
    const delBtn = document.createElement("button");
    delBtn.textContent = "Delete";
    delBtn.className = "delete-btn";
    delBtn.onclick = function () {
      todoList.removeChild(li);
    };
    li.appendChild(delBtn);
    todoList.appendChild(li);
  }

  addBtn.addEventListener("click", function () {
    addTodo(input.value);
    input.value = "";
    input.focus();
  });

  input.addEventListener("keydown", function (e) {
    if (e.key === "Enter") {
      addTodo(input.value);
      input.value = "";
    }
  });
});
