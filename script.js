document.addEventListener("DOMContentLoaded", function () {
  // DOM Elements
  const input = document.getElementById("todo-input");
  const addBtn = document.getElementById("add-btn");
  const todoList = document.getElementById("todo-list");
  const prioritySelect = document.getElementById("priority-select");
  const dueDateInput = document.getElementById("due-date");
  const filterBtns = document.querySelectorAll(".filter-btn");
  const emptyState = document.getElementById("empty-state");
  const progressFill = document.getElementById("progress-fill");
  const progressText = document.getElementById("progress-text");
  const totalTasksEl = document.getElementById("total-tasks");
  const completedTasksEl = document.getElementById("completed-tasks");
  const pendingTasksEl = document.getElementById("pending-tasks");
  const confettiCanvas = document.getElementById("confetti-canvas");

  // State
  let todos = [];
  let currentFilter = "all";
  let editingId = null;

  // Load todos from localStorage with error handling
  try {
    const savedTodos = localStorage.getItem("awesome-todos");
    if (savedTodos) {
      todos = JSON.parse(savedTodos);
    }
  } catch (error) {
    console.error("Error loading todos from localStorage:", error);
    todos = [];
  }

  // Confetti Animation
  const ctx = confettiCanvas.getContext("2d");
  let confettiParticles = [];

  function resizeCanvas() {
    confettiCanvas.width = window.innerWidth;
    confettiCanvas.height = window.innerHeight;
  }

  function createConfetti() {
    const colors = ["#667eea", "#764ba2", "#ff4757", "#ffa502", "#2ed573"];
    for (let i = 0; i < 50; i++) {
      confettiParticles.push({
        x: Math.random() * confettiCanvas.width,
        y: -10,
        vx: (Math.random() - 0.5) * 4,
        vy: Math.random() * 3 + 2,
        color: colors[Math.floor(Math.random() * colors.length)],
        size: Math.random() * 6 + 2,
        rotation: Math.random() * 360,
        rotationSpeed: (Math.random() - 0.5) * 10,
      });
    }
  }

  function animateConfetti() {
    ctx.clearRect(0, 0, confettiCanvas.width, confettiCanvas.height);

    for (let i = confettiParticles.length - 1; i >= 0; i--) {
      const particle = confettiParticles[i];

      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.rotation += particle.rotationSpeed;
      particle.vy += 0.1; // gravity

      ctx.save();
      ctx.translate(particle.x, particle.y);
      ctx.rotate((particle.rotation * Math.PI) / 180);
      ctx.fillStyle = particle.color;
      ctx.fillRect(
        -particle.size / 2,
        -particle.size / 2,
        particle.size,
        particle.size
      );
      ctx.restore();

      if (particle.y > confettiCanvas.height) {
        confettiParticles.splice(i, 1);
      }
    }

    if (confettiParticles.length > 0) {
      requestAnimationFrame(animateConfetti);
    }
  }

  function triggerConfetti() {
    createConfetti();
    animateConfetti();
  }

  // Initialize
  resizeCanvas();
  window.addEventListener("resize", resizeCanvas);

  // Todo Functions
  function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  function saveTodos() {
    try {
      localStorage.setItem("awesome-todos", JSON.stringify(todos));
    } catch (error) {
      console.error("Error saving todos to localStorage:", error);
      // Could show a user notification here
    }
  }

  function formatDate(dateString) {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "Invalid Date";

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      date.setHours(0, 0, 0, 0);

      const diffTime = date - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) return "Overdue";
      if (diffDays === 0) return "Today";
      if (diffDays === 1) return "Tomorrow";
      return date.toLocaleDateString();
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid Date";
    }
  }

  function isOverdue(dateString) {
    if (!dateString) return false;
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return false;

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      date.setHours(0, 0, 0, 0);

      return date < today;
    } catch (error) {
      console.error("Error checking if date is overdue:", error);
      return false;
    }
  }

  function addTodo(text, priority = "low", dueDate = "") {
    const trimmedText = text.trim();
    if (!trimmedText) {
      input.focus();
      return;
    }

    // Validate priority
    const validPriorities = ["low", "medium", "high"];
    if (!validPriorities.includes(priority)) {
      priority = "low";
    }

    // Validate date
    if (dueDate && isNaN(new Date(dueDate).getTime())) {
      dueDate = "";
    }

    const todo = {
      id: generateId(),
      text: trimmedText,
      completed: false,
      priority: priority,
      dueDate: dueDate,
      createdAt: new Date().toISOString(),
    };

    todos.unshift(todo);
    saveTodos();
    renderTodos();
    updateStats();

    // Clear inputs
    input.value = "";
    prioritySelect.value = "low";
    dueDateInput.value = "";
    input.focus();
  }

  function toggleTodo(id) {
    const todo = todos.find((t) => t.id === id);
    if (todo) {
      todo.completed = !todo.completed;
      if (todo.completed) {
        triggerConfetti();
      }
      saveTodos();
      renderTodos();
      updateStats();
    }
  }

  function deleteTodo(id) {
    todos = todos.filter((t) => t.id !== id);
    saveTodos();
    renderTodos();
    updateStats();
  }

  function editTodo(id) {
    const todo = todos.find((t) => t.id === id);
    if (todo) {
      input.value = todo.text;
      prioritySelect.value = todo.priority;
      dueDateInput.value = todo.dueDate;
      editingId = id;
      input.focus();
      addBtn.innerHTML = '<i class="fas fa-save"></i> Update';
    }
  }

  function updateTodo(id, text, priority, dueDate) {
    const todo = todos.find((t) => t.id === id);
    if (todo) {
      todo.text = text.trim();
      todo.priority = priority;
      todo.dueDate = dueDate;
      saveTodos();
      renderTodos();
      updateStats();

      // Reset editing state
      editingId = null;
      addBtn.innerHTML = '<i class="fas fa-plus"></i> Add Task';
    }
  }

  function renderTodos() {
    const filteredTodos = todos.filter((todo) => {
      if (currentFilter === "completed") return todo.completed;
      if (currentFilter === "pending") return !todo.completed;
      return true;
    });

    todoList.innerHTML = "";

    if (filteredTodos.length === 0) {
      emptyState.classList.add("show");
    } else {
      emptyState.classList.remove("show");
    }

    filteredTodos.forEach((todo) => {
      const li = document.createElement("li");
      li.className = `todo-item priority-${todo.priority}`;
      if (todo.completed) li.classList.add("completed");

      const dueDateFormatted = formatDate(todo.dueDate);
      const isTaskOverdue = isOverdue(todo.dueDate);

      // Create checkbox
      const checkbox = document.createElement("div");
      checkbox.className = `todo-checkbox ${todo.completed ? "checked" : ""}`;
      checkbox.onclick = () => toggleTodo(todo.id);

      // Create content
      const content = document.createElement("div");
      content.className = "todo-content";

      const textDiv = document.createElement("div");
      textDiv.className = "todo-text";
      textDiv.textContent = todo.text;

      const metaDiv = document.createElement("div");
      metaDiv.className = "todo-meta";

      const prioritySpan = document.createElement("span");
      prioritySpan.className = `todo-priority ${todo.priority}`;
      prioritySpan.textContent = todo.priority.toUpperCase();
      metaDiv.appendChild(prioritySpan);

      if (todo.dueDate) {
        const dueDateSpan = document.createElement("span");
        dueDateSpan.className = `todo-due-date ${
          isTaskOverdue ? "overdue" : ""
        }`;
        dueDateSpan.innerHTML = `<i class="fas fa-calendar"></i> ${dueDateFormatted}`;
        metaDiv.appendChild(dueDateSpan);
      }

      content.appendChild(textDiv);
      content.appendChild(metaDiv);

      // Create actions
      const actions = document.createElement("div");
      actions.className = "todo-actions";

      const editBtn = document.createElement("button");
      editBtn.className = "action-btn edit-btn";
      editBtn.title = "Edit";
      editBtn.innerHTML = '<i class="fas fa-edit"></i>';
      editBtn.onclick = () => editTodo(todo.id);

      const deleteBtn = document.createElement("button");
      deleteBtn.className = "action-btn delete-btn";
      deleteBtn.title = "Delete";
      deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
      deleteBtn.onclick = () => deleteTodo(todo.id);

      actions.appendChild(editBtn);
      actions.appendChild(deleteBtn);

      // Assemble the todo item
      li.appendChild(checkbox);
      li.appendChild(content);
      li.appendChild(actions);

      todoList.appendChild(li);
    });
  }

  function updateStats() {
    const total = todos.length;
    const completed = todos.filter((t) => t.completed).length;
    const pending = total - completed;
    const progress = total > 0 ? Math.round((completed / total) * 100) : 0;

    totalTasksEl.textContent = total;
    completedTasksEl.textContent = completed;
    pendingTasksEl.textContent = pending;
    progressFill.style.width = `${progress}%`;
    progressText.textContent = `${progress}% Complete`;

    // Animate numbers
    animateNumber(totalTasksEl, total);
    animateNumber(completedTasksEl, completed);
    animateNumber(pendingTasksEl, pending);
  }

  function animateNumber(element, targetNumber) {
    const currentNumber = parseInt(element.textContent) || 0;
    const increment = targetNumber > currentNumber ? 1 : -1;
    const duration = 300;
    const steps = Math.abs(targetNumber - currentNumber);
    const stepDuration = steps > 0 ? duration / steps : 0;

    if (steps === 0) return;

    let current = currentNumber;
    const timer = setInterval(() => {
      current += increment;
      element.textContent = current;

      if (current === targetNumber) {
        clearInterval(timer);
      }
    }, stepDuration);
  }

  function setFilter(filter) {
    currentFilter = filter;
    filterBtns.forEach((btn) => {
      btn.classList.remove("active");
      if (btn.dataset.filter === filter) {
        btn.classList.add("active");
      }
    });
    renderTodos();
  }

  // Event Listeners
  addBtn.addEventListener("click", function () {
    const text = input.value.trim();
    const priority = prioritySelect.value;
    const dueDate = dueDateInput.value;

    if (!text) {
      input.focus();
      return;
    }

    if (editingId) {
      updateTodo(editingId, text, priority, dueDate);
    } else {
      addTodo(text, priority, dueDate);
    }
  });

  input.addEventListener("keydown", function (e) {
    if (e.key === "Enter") {
      addBtn.click();
    }
    if (e.key === "Escape" && editingId) {
      // Cancel editing
      editingId = null;
      input.value = "";
      prioritySelect.value = "low";
      dueDateInput.value = "";
      addBtn.innerHTML = '<i class="fas fa-plus"></i> Add Task';
    }
  });

  filterBtns.forEach((btn) => {
    btn.addEventListener("click", () => {
      setFilter(btn.dataset.filter);
    });
  });

  // No need for global functions since we're using proper event handlers

  // Initialize app
  renderTodos();
  updateStats();
});
