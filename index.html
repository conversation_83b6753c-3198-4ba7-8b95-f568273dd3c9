<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Todo List</title>
    <link rel="stylesheet" href="style.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="background-animation"></div>
    <div class="container">
      <div class="header">
        <h1><i class="fas fa-tasks"></i> My Awesome Tasks</h1>
        <div class="stats">
          <div class="stat-item">
            <span class="stat-number" id="total-tasks">0</span>
            <span class="stat-label">Total</span>
          </div>
          <div class="stat-item">
            <span class="stat-number" id="completed-tasks">0</span>
            <span class="stat-label">Done</span>
          </div>
          <div class="stat-item">
            <span class="stat-number" id="pending-tasks">0</span>
            <span class="stat-label">Pending</span>
          </div>
        </div>
      </div>

      <div class="input-section">
        <div class="input-wrapper">
          <i class="fas fa-plus input-icon"></i>
          <input
            type="text"
            id="todo-input"
            placeholder="What needs to be done? ✨"
          />
          <select id="priority-select">
            <option value="low">🟢 Low</option>
            <option value="medium">🟡 Medium</option>
            <option value="high">🔴 High</option>
          </select>
          <input type="date" id="due-date" />
          <button id="add-btn">
            <i class="fas fa-plus"></i>
            Add Task
          </button>
        </div>
      </div>

      <div class="filter-section">
        <button class="filter-btn active" data-filter="all">All Tasks</button>
        <button class="filter-btn" data-filter="pending">Pending</button>
        <button class="filter-btn" data-filter="completed">Completed</button>
      </div>

      <div class="progress-section">
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
        <span class="progress-text" id="progress-text">0% Complete</span>
      </div>

      <ul id="todo-list"></ul>

      <div class="empty-state" id="empty-state">
        <i class="fas fa-clipboard-check"></i>
        <h3>No tasks yet!</h3>
        <p>Add your first task above to get started 🚀</p>
      </div>
    </div>

    <!-- Confetti Canvas -->
    <canvas id="confetti-canvas"></canvas>

    <script src="script.js"></script>
  </body>
</html>
